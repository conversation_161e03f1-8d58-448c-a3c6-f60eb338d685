# Subscription Management System

A comprehensive subscription-based quota and feature management system that integrates with the existing SystemSettings model to manage organization subscriptions, user quotas, LINE account limits, storage quotas, and feature access control.

## Overview

The subscription system provides:
- **User Quota Management**: Limits the number of active users based on subscription tier
- **LINE Account Quota Management**: Controls the number of LINE bot accounts that can be created
- **Storage Quota Monitoring**: Tracks Azure Blob Storage usage against subscription limits
- **Feature Access Control**: Controls access to premium features based on subscription tier
- **Subscription Status Tracking**: Monitors subscription status, expiry, and activation dates
- **API Integration**: RESTful APIs for quota validation and subscription management
- **Real-time Validation**: Automatic quota checks during user and LINE account creation

## Architecture

The subscription system follows a service-oriented architecture with clear separation of concerns:

```mermaid
graph TB
    subgraph "API Layer"
        A[SubscriptionInfoView]
        B[QuotaStatusView]
        C[FeatureAccessView]
        D[UserCreationValidationView]
        E[LineAccountCreationValidationView]
    end

    subgraph "Service Layer"
        F[SubscriptionDataService]
        G[QuotaValidationService]
        H[StorageQuotaService]
    end

    subgraph "Data Layer"
        I[SystemSettings Model]
        J[User Model]
        K[LineUserProfile Model]
        L[Azure Blob Storage]
    end

    subgraph "Integration Points"
        M[SignUpView]
        N[Management Commands]
        O[init_subscription]
    end

    A --> F
    B --> G
    C --> G
    D --> G
    E --> G

    F --> I
    G --> F
    G --> J
    G --> K
    H --> L
    G --> H

    M --> G
    N --> F
    O --> F

    style F fill:#e1f5fe
    style G fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#f3e5f5
```

### Data Storage Architecture
- **Primary Storage**: Uses existing `SystemSettings` model with key `SUBSCRIPTION_DATA`
- **Storage Format**: JSON data stored in the `value` field with `value_type='json'`
- **No Additional Tables**: Leverages existing infrastructure without database schema changes
- **Immediate Application**: Changes take effect immediately without requiring management commands

### Core Components

1. **SubscriptionDataService**:
   - Manages subscription data storage and retrieval from SystemSettings
   - Handles JSON serialization/deserialization
   - Provides subscription expiry validation

2. **QuotaValidationService**:
   - Validates user creation against subscription limits
   - Checks LINE account creation quotas
   - Provides comprehensive quota status reporting
   - Validates feature access permissions

3. **StorageQuotaService**:
   - Integrates with Azure Blob Storage for usage monitoring
   - Provides storage metrics for quota enforcement
   - Handles storage service unavailability gracefully

4. **API Views**:
   - RESTful endpoints for subscription management
   - Role-based access control (Admin-only for sensitive operations)
   - Comprehensive error handling and logging

5. **User Creation Integration**:
   - Automatic quota validation in SignUpView
   - Prevents user creation when limits are exceeded
   - Provides detailed error messages and quota information

## Implementation Details

### Data Storage Approach

The subscription system uses the existing `SystemSettings` model with the following configuration:
- **Key**: `SUBSCRIPTION_DATA`
- **Value Type**: `json`
- **Description**: "Organization subscription data"
- **Storage**: JSON data in the `value` field

This approach provides several advantages:
- **No Schema Changes**: Leverages existing infrastructure
- **Immediate Updates**: Changes apply instantly without migrations
- **Audit Trail**: Built-in tracking via `updated_by` and `updated_on` fields
- **Flexibility**: JSON structure can evolve without database changes

### Business Logic and Validation Rules

#### User Quota Validation
- **Built-in Account Exclusion**: Automatically excludes 2 built-in Admin and System accounts from quota calculations
- **Active User Counting**: Only counts `is_active=True` users against the quota
- **Pre-creation Validation**: Checks quota before user creation to prevent overages
- **Graceful Degradation**: Provides detailed error messages when quotas are exceeded

#### LINE Account Quota Validation
- **Unlimited Support**: Handles "unlimited" quota values for enterprise tiers
- **Current Usage Tracking**: Counts existing LINE accounts against subscription limits
- **Integration Ready**: Designed to integrate with LINE account creation workflows

#### Storage Quota Monitoring
- **Azure Integration**: Connects to Azure Blob Storage for real-time usage data
- **Fault Tolerance**: Continues operation even when storage metrics are unavailable
- **Comprehensive Metrics**: Tracks total storage size (GB) and blob count

#### Subscription Status Validation
- **Expiry Checking**: Validates subscription expiration dates
- **Status Enforcement**: Ensures only "active" subscriptions allow operations
- **Timezone Awareness**: Uses UTC for consistent expiry calculations

### JSON Data Structure

The subscription data follows a comprehensive schema that supports multiple subscription tiers and features:

```json
{
    "organization_name": "Viriyah",
    "subscription_key": "ENTP-C3D4E5F6G7H8I9J0",
    "tier_id": "enterprise_plus",
    "tier_name": "Enterprise Plus",
    "status": "active",
    "activated_on": "2025-08-29T22:15:00Z",
    "expires_at": "2025-12-31T23:59:59Z",
    "quota": {
        "max_active_users": 50,
        "max_line_accounts": "unlimited",
        "max_ai_workflow_units": "unlimited",
        "max_messages_per_min": 200,
        "max_storage_gb": 2000
    },
    "features": {
        "custom_transfer_algo": true,
        "custom_case_desc": true,
        "custom_ai_workflow": true,
        "ai_quick_reply": true,
        "ai_smart_reply": true,
        "ai_memory": true,
        "crm_integration": true,
        "crm_notify_claim": true,
        "crm_case_system": true,
        "dashboard_sla_config": true,
        "dashboard_sla_alert": true,
        "broadcasting": true
    },
    "metadata": {
        "billing_contact": "<EMAIL>",
        "technical_contact": "<EMAIL>"
    }
}
```

### Predefined Subscription Tiers

The system includes three predefined subscription tiers with different feature sets and quotas:

#### Premium Tier
- **Max Users**: 5
- **LINE Accounts**: 5
- **AI Workflow Units**: 1
- **Messages/Min**: 30
- **Storage**: 250 GB
- **Key Features**: Basic AI quick reply, broadcasting

#### Enterprise Tier
- **Max Users**: 20
- **LINE Accounts**: 10
- **AI Workflow Units**: 5
- **Messages/Min**: 100
- **Storage**: 1000 GB
- **Key Features**: Full AI suite, CRM integration, SLA management

#### Enterprise Plus Tier
- **Max Users**: Unlimited
- **LINE Accounts**: Unlimited
- **AI Workflow Units**: Unlimited
- **Messages/Min**: 200
- **Storage**: 2000 GB
- **Key Features**: All features enabled, maximum flexibility

## API Endpoints

The subscription system provides comprehensive RESTful APIs for quota validation and subscription management. All endpoints require authentication, and some require admin privileges.

### 1. Get Quota Status
```http
GET /api/subscription/quota/status/
Authorization: Bearer <jwt_token>
```

**Description**: Returns comprehensive quota information including current usage across all resources.

**Access**: Admin only (`is_superuser=True`)

**Response:**
```json
{
    "message": "Quota status retrieved successfully",
    "data": {
        "organization_name": "Your Company",
        "tier_name": "Enterprise",
        "status": "active",
        "expires_at": "2025-12-31T23:59:59Z",
        "quota": {
            "max_active_users": 20,
            "current_active_users": 15,
            "max_line_accounts": 10,
            "current_line_accounts": 3,
            "max_ai_workflow_units": 5,
            "current_ai_workflow_units": 1,
            "max_messages_per_min": 100,
            "max_storage_gb": 1000,
            "current_storage_gb": 245.7
        },
        "features": {
            "ai_quick_reply": true,
            "crm_integration": true
        }
    }
}
```

### 2. Check User Creation Quota
```http
GET /api/subscription/quota/user-creation-check/
Authorization: Bearer <jwt_token>
```

**Description**: Validates if a new user can be created based on current subscription quota.

**Access**: Admin only (`is_superuser=True`)

**Response:**
```json
{
    "message": "User creation allowed",
    "data": {
        "allowed": true,
        "quota_info": {
            "current_users": 15,
            "max_users": 20,
            "remaining_slots": 5
        }
    }
}
```

### 3. Check LINE Account Creation Quota
```http
GET /api/subscription/quota/line-account-creation-check/
Authorization: Bearer <jwt_token>
```

**Description**: Validates if a new LINE account can be created based on current subscription quota.

**Access**: Admin only (`is_superuser=True`)

**Response:**
```json
{
    "message": "LINE account creation allowed",
    "data": {
        "allowed": true,
        "quota_info": {
            "current_line_accounts": 3,
            "max_line_accounts": 10,
            "remaining_slots": 7
        }
    }
}
```

### 4. Check Feature Access
```http
POST /api/subscription/feature/check/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
    "feature_name": "ai_quick_reply"
}
```

**Description**: Validates if a specific feature is enabled in the current subscription.

**Access**: Authenticated users

**Response:**
```json
{
    "message": "Feature access checked successfully",
    "data": {
        "feature_name": "ai_quick_reply",
        "enabled": true
    }
}
```

### 5. Get Subscription Info
```http
GET /api/subscription/info/
Authorization: Bearer <jwt_token>
```

**Description**: Returns subscription information filtered based on user permissions.

**Access**: Authenticated users (limited info for non-admins)

**Admin Response:**
```json
{
    "message": "Subscription information retrieved successfully",
    "data": {
        "organization_name": "Your Company",
        "tier_name": "Enterprise",
        "status": "active",
        "expires_at": "2025-12-31T23:59:59Z",
        "features": {...},
        "quota": {...}
    }
}
```

**Non-Admin Response:**
```json
{
    "message": "Subscription information retrieved successfully",
    "data": {
        "tier_name": "Enterprise",
        "status": "active",
        "features": {...}
    }
}
```

## Usage Examples

### 1. Initialize Subscription Data

Use the management command to set up subscription data for different tiers:

```bash
# Initialize Premium tier
python manage.py init_subscription --tier premium --company "Your Company" --expires 2025-12-31

# Initialize Enterprise tier
python manage.py init_subscription --tier enterprise --company "Your Company" --expires 2025-12-31

# Initialize Enterprise Plus tier
python manage.py init_subscription --tier enterprise_plus --company "Your Company" --expires 2025-12-31
```

### 2. Check User Creation Quota

```python
from subscription.services import QuotaValidationService

# Check if user creation is allowed
result = QuotaValidationService.can_create_user()
if result['allowed']:
    # Proceed with user creation
    user = User.objects.create_user(...)
    print(f"User created. Remaining slots: {result['quota_info']['remaining_slots']}")
else:
    # Handle quota exceeded
    print(f"Cannot create user: {result['error']}")
```

### 3. Check LINE Account Creation Quota

```python
from subscription.services import QuotaValidationService

# Check if LINE account creation is allowed
result = QuotaValidationService.can_create_line_account()
if result['allowed']:
    # Proceed with LINE account creation
    line_account = LineUserProfile.objects.create(...)
else:
    print(f"Cannot create LINE account: {result['error']}")
```

### 4. Validate Feature Access

```python
from subscription.services import QuotaValidationService

# Check if specific feature is available
features_to_check = ['ai_quick_reply', 'crm_integration', 'custom_ai_workflow']

for feature in features_to_check:
    has_access = QuotaValidationService.check_feature_access(feature)
    if has_access:
        print(f"✓ {feature} is enabled")
    else:
        print(f"✗ {feature} is not available in current subscription")
```

### 5. Get Comprehensive Quota Status

```python
from subscription.services import QuotaValidationService

# Get detailed quota information
quota_status = QuotaValidationService.get_quota_status()

if 'error' not in quota_status:
    print(f"Organization: {quota_status['organization_name']}")
    print(f"Tier: {quota_status['tier_name']}")
    print(f"Status: {quota_status['status']}")
    print(f"Expires: {quota_status['expires_at']}")

    quota = quota_status['quota']
    print(f"Users: {quota['current_active_users']}/{quota['max_active_users']}")
    print(f"Storage: {quota['current_storage_gb']:.1f}/{quota['max_storage_gb']} GB")
else:
    print(f"Error retrieving quota: {quota_status['error']}")
```

### 6. Update Subscription Data

```python
from subscription.services import SubscriptionDataService
from django.contrib.auth import get_user_model

User = get_user_model()

# Get current subscription data
current_data = SubscriptionDataService.get_subscription_data()

# Modify subscription data
current_data['quota']['max_active_users'] = 50
current_data['features']['crm_integration'] = True

# Save updated data
admin_user = User.objects.filter(is_superuser=True).first()
SubscriptionDataService.save_subscription_data(current_data, user=admin_user)

print("Subscription data updated successfully")
```

## Integration Points

### User Creation Integration

The subscription system seamlessly integrates with the existing `SignUpView` in `user/views.py`:

**Pre-validation Process:**
1. **Quota Check**: Before user creation, `QuotaValidationService.can_create_user()` is called
2. **Subscription Status**: Validates that subscription is active and not expired
3. **User Count**: Checks current active users against subscription limits
4. **Built-in Account Exclusion**: Automatically excludes 2 built-in Admin and System accounts

**Error Handling:**
- Returns HTTP 403 with detailed error message when quota is exceeded
- Provides quota information in error responses
- Includes metadata flags for frontend handling

**Success Enhancement:**
- Includes remaining quota slots in successful user creation responses
- Provides subscription tier and organization information
- Updates quota information after user creation

### LINE Account Integration

The system is designed to integrate with LINE account creation workflows:

```python
# Example integration in LINE account creation view
from subscription.services import QuotaValidationService

def create_line_account(request):
    # Check quota before creating LINE account
    quota_check = QuotaValidationService.can_create_line_account()

    if not quota_check['allowed']:
        return Response({
            "error": quota_check['error'],
            "quota_info": quota_check.get('quota_info', {})
        }, status=403)

    # Proceed with LINE account creation
    line_account = LineUserProfile.objects.create(...)
    return Response({"message": "LINE account created successfully"})
```

## Configuration

### Environment Variables

The subscription system integrates with existing Azure storage configuration:

```bash
# Azure Blob Storage (for storage quota monitoring)
AZURE_ACCOUNT_NAME=your_storage_account
AZURE_ACCOUNT_KEY=your_storage_key
AZURE_CONTAINER=your_container_name
```

### Django Settings

No additional Django settings are required. The system uses existing configurations:

- **Authentication**: Uses existing JWT authentication
- **Permissions**: Leverages existing user permission system
- **Database**: Uses existing database configuration
- **Logging**: Integrates with existing logging configuration

### SystemSettings Configuration

The subscription data is stored in `SystemSettings` with these properties:

```python
{
    'key': 'SUBSCRIPTION_DATA',
    'value_type': 'json',
    'description': 'Organization subscription data',
    'is_sensitive': False,
    'requires_restart': False
}
```

## Dependencies

### Required Packages

The subscription system relies on existing project dependencies:

```python
# Core Django packages (already in project)
django>=4.0
djangorestframework>=3.14
djangorestframework-simplejwt>=5.0

# Azure integration (already in project)
azure-storage-blob>=12.0

# Database (already in project)
psycopg2-binary>=2.9  # for PostgreSQL
```

### External Services

**Azure Blob Storage** (Optional):
- Used for storage quota monitoring
- Gracefully handles service unavailability
- Provides storage usage metrics for quota enforcement

**No additional external services required** - the system is designed to work with existing infrastructure.

## Error Handling

The system provides comprehensive error handling across all components:

### Service Layer Errors

**SubscriptionDataService:**
- `SystemSettings.DoesNotExist`: Returns `None` when no subscription data exists
- JSON parsing errors: Logged and handled gracefully
- Database connection issues: Propagated with appropriate error messages

**QuotaValidationService:**
- No subscription found: Returns `{'allowed': False, 'error': 'No active subscription found'}`
- Expired subscription: Returns `{'allowed': False, 'error': 'Subscription has expired'}`
- Quota exceeded: Returns `{'allowed': False, 'error': 'User limit reached'}`
- Invalid quota data: Returns appropriate error messages

**StorageQuotaService:**
- Azure service unavailable: Logs warning and returns `None`
- Authentication failures: Logged with error details
- Network timeouts: Handled gracefully without breaking quota checks

### API Layer Errors

**Authentication Errors:**
- Missing JWT token: Returns HTTP 401 Unauthorized
- Invalid token: Returns HTTP 401 Unauthorized
- Expired token: Returns HTTP 401 Unauthorized

**Authorization Errors:**
- Non-admin accessing admin endpoints: Returns HTTP 403 Forbidden
- Missing permissions: Returns HTTP 403 Forbidden

**Validation Errors:**
- Invalid feature names: Returns HTTP 400 Bad Request
- Malformed request data: Returns HTTP 400 Bad Request

**System Errors:**
- Database connection issues: Returns HTTP 500 Internal Server Error
- Unexpected exceptions: Logged and returns HTTP 500 Internal Server Error

## Testing

### Running Tests

```bash
# Run all subscription tests
python manage.py test subscription

# Run specific test classes
python manage.py test subscription.tests.SubscriptionDataServiceTest
python manage.py test subscription.tests.QuotaValidationServiceTest
python manage.py test subscription.tests.SubscriptionAPITest

# Run with verbose output
python manage.py test subscription --verbosity=2
```

### Test Coverage

The test suite provides comprehensive coverage:

**Service Layer Tests:**
- Subscription data storage and retrieval
- Quota validation logic for users and LINE accounts
- Feature access control
- Subscription expiry validation
- Error scenarios and edge cases

**API Layer Tests:**
- Authentication and authorization
- Request/response validation
- Error handling and status codes
- Permission-based data filtering

**Integration Tests:**
- User creation workflow integration
- Quota enforcement during operations
- Real-world usage scenarios

## Security Considerations

### Access Control

**Admin-Only Operations:**
- Quota status retrieval
- User creation validation
- LINE account creation validation
- Full subscription information access

**User-Level Operations:**
- Feature access checking
- Limited subscription information
- Personal quota impact visibility

### Data Protection

**Sensitive Information:**
- Subscription keys are stored but not exposed in API responses
- Billing and technical contact information restricted to admins
- Usage metrics filtered based on user permissions

**Input Validation:**
- All API inputs validated before processing
- SQL injection prevention through ORM usage
- XSS prevention through proper serialization

### Audit Trail

**Change Tracking:**
- All subscription data changes tracked via `updated_by` field
- Timestamp tracking via `updated_on` field
- Management command execution logging

**Error Logging:**
- Comprehensive error logging for debugging
- Security event logging for unauthorized access attempts
- Performance monitoring for quota check operations

## Management Commands

### init_subscription

Initialize or update subscription data with predefined tier configurations.

**Usage:**
```bash
python manage.py init_subscription --tier <tier_name> --company <company_name> [--expires <date>]
```

**Parameters:**
- `--tier`: Required. Choose from `premium`, `enterprise`, or `enterprise_plus`
- `--company`: Optional. Company/Organization name (default: empty string)
- `--expires`: Optional. Expiration date in YYYY-MM-DD format (default: 2025-12-31)

**Examples:**
```bash
# Initialize Premium tier for "Acme Corp"
python manage.py init_subscription --tier premium --company "Acme Corp"

# Initialize Enterprise Plus with custom expiry
python manage.py init_subscription --tier enterprise_plus --company "Tech Solutions" --expires 2026-06-30

# Update existing subscription to Enterprise tier
python manage.py init_subscription --tier enterprise --company "Updated Company Name"
```

**Output:**
```
Successfully created subscription data for Acme Corp with Premium tier (5 max users)
Current active users: 3/5
```

## Monitoring and Maintenance

### Storage Usage Monitoring

The system automatically monitors Azure Blob Storage usage:

```python
from subscription.services import StorageQuotaService

# Get current storage usage
usage = StorageQuotaService.get_storage_usage()
if usage:
    print(f"Storage used: {usage['total_size_gb']:.2f} GB")
    print(f"Total blobs: {usage['total_blob_count']}")
else:
    print("Storage usage unavailable")
```

### Subscription Health Checks

Regular health checks can be implemented:

```python
from subscription.services import SubscriptionDataService, QuotaValidationService

def check_subscription_health():
    """Perform comprehensive subscription health check"""

    # Check if subscription exists
    data = SubscriptionDataService.get_subscription_data()
    if not data:
        return {"status": "error", "message": "No subscription found"}

    # Check subscription status
    if data.get('status') != 'active':
        return {"status": "warning", "message": f"Subscription is {data.get('status')}"}

    # Check expiry
    if SubscriptionDataService.is_subscription_expired():
        return {"status": "error", "message": "Subscription has expired"}

    # Check quota usage
    quota_status = QuotaValidationService.get_quota_status()
    if 'error' in quota_status:
        return {"status": "error", "message": quota_status['error']}

    # Calculate usage percentages
    quota = quota_status['quota']
    user_usage = (quota['current_active_users'] / quota['max_active_users']) * 100

    warnings = []
    if user_usage > 90:
        warnings.append(f"User quota at {user_usage:.1f}% capacity")

    if quota.get('current_storage_gb', 0) / quota.get('max_storage_gb', 1) > 0.9:
        warnings.append("Storage quota above 90% capacity")

    return {
        "status": "healthy" if not warnings else "warning",
        "message": "Subscription is healthy",
        "warnings": warnings,
        "quota_usage": {
            "users": f"{quota['current_active_users']}/{quota['max_active_users']}",
            "storage": f"{quota.get('current_storage_gb', 0):.1f}/{quota.get('max_storage_gb', 0)} GB"
        }
    }
```

### Automated Alerts

Set up automated alerts for quota thresholds:

```python
from django.core.mail import send_mail
from django.conf import settings

def check_and_alert_quota_usage():
    """Check quota usage and send alerts if thresholds are exceeded"""

    quota_status = QuotaValidationService.get_quota_status()
    if 'error' in quota_status:
        return

    quota = quota_status['quota']
    alerts = []

    # Check user quota (alert at 80% and 95%)
    user_percentage = (quota['current_active_users'] / quota['max_active_users']) * 100
    if user_percentage >= 95:
        alerts.append(f"CRITICAL: User quota at {user_percentage:.1f}% ({quota['current_active_users']}/{quota['max_active_users']})")
    elif user_percentage >= 80:
        alerts.append(f"WARNING: User quota at {user_percentage:.1f}% ({quota['current_active_users']}/{quota['max_active_users']})")

    # Check storage quota
    if quota.get('current_storage_gb') and quota.get('max_storage_gb'):
        storage_percentage = (quota['current_storage_gb'] / quota['max_storage_gb']) * 100
        if storage_percentage >= 95:
            alerts.append(f"CRITICAL: Storage quota at {storage_percentage:.1f}% ({quota['current_storage_gb']:.1f}/{quota['max_storage_gb']} GB)")
        elif storage_percentage >= 80:
            alerts.append(f"WARNING: Storage quota at {storage_percentage:.1f}% ({quota['current_storage_gb']:.1f}/{quota['max_storage_gb']} GB)")

    # Send alerts if any
    if alerts:
        subject = f"Subscription Quota Alert - {quota_status['organization_name']}"
        message = "\n".join(alerts)

        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>'],
            fail_silently=False,
        )
```

## Troubleshooting

### Common Issues

**1. "No active subscription found" Error**
```bash
# Check if subscription data exists
python manage.py shell
>>> from subscription.services import SubscriptionDataService
>>> data = SubscriptionDataService.get_subscription_data()
>>> print(data)

# If None, initialize subscription
python manage.py init_subscription --tier enterprise --company "Your Company"
```

**2. Storage Usage Not Available**
```bash
# Check Azure storage configuration
python manage.py shell
>>> from django.conf import settings
>>> print(f"Account: {settings.AZURE_ACCOUNT_NAME}")
>>> print(f"Container: {settings.AZURE_CONTAINER}")

# Test Azure connection
>>> from devproject.utils.azure_storage import AzureBlobStorage
>>> storage = AzureBlobStorage()
>>> usage = storage.get_storage_usage()
>>> print(usage)
```

**3. User Creation Still Blocked After Quota Increase**
```bash
# Check current user count
python manage.py shell
>>> from django.contrib.auth import get_user_model
>>> User = get_user_model()
>>> active_users = User.objects.filter(is_active=True).count()
>>> print(f"Active users: {active_users}")
>>> print(f"Excluding built-ins: {active_users - 2}")
```

**4. Feature Access Not Working**
```bash
# Check subscription features
python manage.py shell
>>> from subscription.services import SubscriptionDataService
>>> data = SubscriptionDataService.get_subscription_data()
>>> print(data.get('features', {}))

# Test specific feature
>>> from subscription.services import QuotaValidationService
>>> has_access = QuotaValidationService.check_feature_access('ai_quick_reply')
>>> print(f"AI Quick Reply access: {has_access}")
```

### Debug Mode

Enable debug logging for subscription operations:

```python
# In settings.py or local settings
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'subscription': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

## Migration Guide

### From Manual Quota Management

If migrating from a manual quota management system:

1. **Backup existing data**
2. **Initialize subscription with current settings**
3. **Update user creation workflows**
4. **Test quota enforcement**
5. **Monitor for issues**

### Upgrading Subscription Tiers

To upgrade an existing subscription:

```python
from subscription.services import SubscriptionDataService
from django.contrib.auth import get_user_model

# Get current data
current_data = SubscriptionDataService.get_subscription_data()

# Update tier information
current_data.update({
    "tier_id": "enterprise_plus",
    "tier_name": "Enterprise Plus",
    "quota": {
        "max_active_users": "unlimited",
        "max_line_accounts": "unlimited",
        "max_ai_workflow_units": "unlimited",
        "max_messages_per_min": 200,
        "max_storage_gb": 2000
    },
    "features": {
        # Enable all features for Enterprise Plus
        "custom_transfer_algo": True,
        "custom_case_desc": True,
        "custom_ai_workflow": True,
        "ai_quick_reply": True,
        "ai_smart_reply": True,
        "ai_memory": True,
        "crm_integration": True,
        "crm_notify_claim": True,
        "crm_case_system": True,
        "dashboard_sla_config": True,
        "dashboard_sla_alert": True,
        "broadcasting": True
    }
})

# Save updated data
User = get_user_model()
admin_user = User.objects.filter(is_superuser=True).first()
SubscriptionDataService.save_subscription_data(current_data, user=admin_user)
```

---

## Summary

The Subscription Management System provides a comprehensive, scalable solution for managing organization subscriptions, user quotas, and feature access. Key benefits include:

- **Zero Database Impact**: Uses existing SystemSettings infrastructure
- **Immediate Updates**: Changes apply instantly without migrations
- **Comprehensive Monitoring**: Tracks users, LINE accounts, storage, and features
- **Robust Error Handling**: Graceful degradation and detailed error reporting
- **Security-First Design**: Role-based access and comprehensive audit trails
- **Easy Integration**: Seamless integration with existing user workflows
- **Flexible Configuration**: Support for multiple subscription tiers and custom features

The system is production-ready and designed to scale with your organization's needs while maintaining simplicity and reliability.
